<Window x:Class="wpfprinterapp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:wpfprinterapp"
        mc:Ignorable="d"
        Title="智能标签打印系统" Height="950" Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="#f8fafc">

    <Window.Resources>
        <!-- 现代化样式定义 -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="12" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#e2e8f0" />
            <Setter Property="Background" Value="White" />
            <Setter Property="Foreground" Value="#1e293b" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost"
                                          Margin="{TemplateBinding Padding}"
                                          VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#3b82f6" />
                                <Setter Property="Background" Value="White" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernComboBox" TargetType="ComboBox">
            <Setter Property="ItemContainerStyle">
                <Setter.Value>
                    <Style TargetType="ComboBoxItem">
                        <Setter Property="Padding" Value="8,10" />
                        <Setter Property="FontSize" Value="14" />
                        <Setter Property="HorizontalContentAlignment" Value="Left" />
                        <Setter Property="VerticalContentAlignment" Value="Center" />
                        <Setter Property="Background" Value="Transparent" />
                        <Setter Property="Focusable" Value="True" />
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="ComboBoxItem">
                                    <Border x:Name="Border" Background="{TemplateBinding Background}"
                                            SnapsToDevicePixels="True">
                                        <ContentPresenter
                                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                            Margin="{TemplateBinding Padding}" />
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsHighlighted" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="#f1f5f9" />
                                        </Trigger>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="#e0e7ef" />
                                        </Trigger>
                                        <Trigger Property="IsEnabled" Value="False">
                                            <Setter Property="Foreground" Value="#b0b0b0" />
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </Setter.Value>
            </Setter>
            <Setter Property="Padding" Value="12" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="BorderBrush" Value="#e2e8f0" />
            <Setter Property="Background" Value="White" />
            <Setter Property="Foreground" Value="#1e293b" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBox">
                        <Grid>
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="6">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <!-- 显示选中项的区域 -->
                                    <ContentPresenter Grid.Column="0"
                                                      Name="ContentSite"
                                                      Margin="{TemplateBinding Padding}"
                                                      VerticalAlignment="Center"
                                                      HorizontalAlignment="Left"
                                                      Content="{TemplateBinding SelectionBoxItem}"
                                                      ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                      ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                                      IsHitTestVisible="False" />

                                    <!-- 可编辑文本框（当IsEditable=True时显示） -->
                                    <TextBox Grid.Column="0"
                                             Name="PART_EditableTextBox"
                                             Margin="{TemplateBinding Padding}"
                                             VerticalAlignment="Center"
                                             HorizontalAlignment="Stretch"
                                             Background="Transparent"
                                             BorderThickness="0"
                                             Foreground="{TemplateBinding Foreground}"
                                             FontFamily="{TemplateBinding FontFamily}"
                                             FontSize="{TemplateBinding FontSize}"
                                             Visibility="Hidden"
                                             IsReadOnly="{TemplateBinding IsReadOnly}" />

                                    <!-- 下拉按钮 -->
                                    <ToggleButton Grid.Column="1"
                                                  Name="ToggleButton"
                                                  Background="Transparent"
                                                  BorderThickness="0"
                                                  Width="30"
                                                  Height="{TemplateBinding Height}"
                                                  IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                                  ClickMode="Press"
                                                  Focusable="False">
                                        <Path Data="M 0 0 L 4 4 L 8 0 Z"
                                              Fill="#64748b"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center" />
                                    </ToggleButton>
                                </Grid>
                            </Border>

                            <!-- 下拉列表 -->
                            <Popup Name="Popup"
                                   Placement="Bottom"
                                   IsOpen="{TemplateBinding IsDropDownOpen}"
                                   AllowsTransparency="True"
                                   Focusable="False"
                                   PopupAnimation="Slide">
                                <Border Background="White"
                                        BorderBrush="#e2e8f0"
                                        BorderThickness="1"
                                        CornerRadius="6"
                                        MinWidth="{TemplateBinding ActualWidth}"
                                        Margin="0,10,0,10">
                                    <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True">
                                        <StackPanel IsItemsHost="True"
                                                    KeyboardNavigation.DirectionalNavigation="Contained" />
                                    </ScrollViewer>
                                </Border>
                            </Popup>
                        </Grid>

                        <ControlTemplate.Triggers>
                            <!-- 焦点状态 -->
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#3b82f6" />
                            </Trigger>

                            <!-- 可编辑状态 -->
                            <Trigger Property="IsEditable" Value="True">
                                <Setter TargetName="PART_EditableTextBox" Property="Visibility" Value="Visible" />
                                <Setter TargetName="ContentSite" Property="Visibility" Value="Hidden" />
                            </Trigger>

                            <!-- 鼠标悬停状态 -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="BorderBrush" Value="#94a3b8" />
                            </Trigger>

                            <!-- 禁用状态 -->
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Foreground" Value="#94a3b8" />
                                <Setter Property="Background" Value="#f1f5f9" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#3b82f6" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Padding" Value="16,12" />
            <Setter Property="FontSize" Value="16" />
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#2563eb" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1d4ed8" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernRadioButton" TargetType="RadioButton">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Foreground" Value="#1e293b" />
            <Setter Property="Margin" Value="0,0,20,0" />
        </Style>

        <Style x:Key="LabelStyle" TargetType="Label">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Foreground" Value="#64748b" />
            <Setter Property="FontWeight" Value="Medium" />
            <Setter Property="Margin" Value="0,0,0,8" />
        </Style>

        <Style x:Key="SectionHeaderStyle" TargetType="Label">
            <Setter Property="FontSize" Value="20" />
            <Setter Property="Foreground" Value="#1e293b" />
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="Margin" Value="0,0,0,20" />
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="1*" />
            <ColumnDefinition Width="1.2*" />
        </Grid.ColumnDefinitions>

        <!-- 左侧配置面板 -->
        <Border Grid.Column="0"
                Background="White"
                CornerRadius="12"
                Padding="30"
                Margin="0,0,10,0"
                BorderThickness="1"
                BorderBrush="#e2e8f0">
            <Border.Effect>
                <DropShadowEffect Color="#000000"
                                  Opacity="0.1"
                                  ShadowDepth="4"
                                  BlurRadius="20" />
            </Border.Effect>

            <StackPanel>
                <Label Content="配置项" Style="{StaticResource SectionHeaderStyle}" />

                <!-- 产品编号 -->
                <Label Content="产品编号" Style="{StaticResource LabelStyle}" />
                <ComboBox x:Name="ProductCodeComboBox"
                          Style="{StaticResource ModernComboBox}"
                          IsEditable="True"
                          IsReadOnly="False"
                          Margin="0,0,0,20"
                          SelectionChanged="ProductCodeComboBox_SelectionChanged" />

                <!-- 颜色 -->
                <Label Content="颜色" Style="{StaticResource LabelStyle}" />
                <ComboBox x:Name="ColorComboBox"
                          Style="{StaticResource ModernComboBox}"
                          IsEditable="True"
                          IsReadOnly="True"
                          Margin="0,0,0,20"
                          SelectionChanged="ColorComboBox_SelectionChanged" />

                <!-- 尺码类型 -->
                <Label Content="尺码类型" Style="{StaticResource LabelStyle}" />
                <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                    <RadioButton x:Name="CharSizeRadio"
                                 Content="字符尺码"
                                 Style="{StaticResource ModernRadioButton}"
                                 IsChecked="True" />
                    <RadioButton x:Name="NumSizeRadio"
                                 Content="数字尺码"
                                 Style="{StaticResource ModernRadioButton}" />
                </StackPanel>

                <!-- 尺码 -->
                <Label Content="尺码" Style="{StaticResource LabelStyle}" />
                <ComboBox x:Name="SizeComboBox"
                          Style="{StaticResource ModernComboBox}"
                          Margin="0,0,0,20"
                          SelectionChanged="SizeComboBox_SelectionChanged" />

                <!-- 单位和库存数量 -->
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*" />
                        <ColumnDefinition Width="1*" />
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <Label Content="单位" Style="{StaticResource LabelStyle}" />
                        <TextBox x:Name="UnitTextBox"
                                 Style="{StaticResource ModernTextBox}"
                                 IsReadOnly="True"
                                 Background="#f1f5f9" />
                    </StackPanel>

                    <StackPanel Grid.Column="1" Margin="10,0,0,0">
                        <Label Content="库存数量" Style="{StaticResource LabelStyle}" />
                        <TextBox x:Name="QuantityTextBox"
                                 Style="{StaticResource ModernTextBox}"
                                 Text="100"
                                 TextChanged="QuantityTextBox_TextChanged" />
                    </StackPanel>
                </Grid>

                <!-- 打印张数 -->
                <Label Content="打印张数" Style="{StaticResource LabelStyle}" />
                <TextBox x:Name="PrintCountTextBox"
                         Style="{StaticResource ModernTextBox}"
                         Text="5"
                         Margin="0,0,0,30" />

                <!-- 打印按钮 -->
                <Button Content="打 印"
                        Style="{StaticResource ModernButton}"
                        Click="PrintButton_Click" />
            </StackPanel>
        </Border>

        <!-- 右侧预览面板 -->
        <Border Grid.Column="1"
                Background="White"
                CornerRadius="12"
                Padding="30"
                Margin="10,0,0,0"
                BorderThickness="1"
                BorderBrush="#e2e8f0">
            <Border.Effect>
                <DropShadowEffect Color="#000000"
                                  Opacity="0.1"
                                  ShadowDepth="4"
                                  BlurRadius="20" />
            </Border.Effect>

            <StackPanel>
                <Label Content="标签预览"
                       Style="{StaticResource SectionHeaderStyle}"
                       Foreground="#64748b" />

                <!-- 标签预览区域 -->
                <Border Background="White"
                        BorderBrush="#e2e8f0"
                        BorderThickness="2"
                        CornerRadius="8"
                        Padding="40"
                        MinHeight="350">
                    <Border.Effect>
                        <DropShadowEffect Color="#000000"
                                          Opacity="0.05"
                                          ShadowDepth="2"
                                          BlurRadius="10" />
                    </Border.Effect>

                    <StackPanel HorizontalAlignment="Center"
                                VerticalAlignment="Center">

                        <!-- SKU信息 -->
                        <TextBlock x:Name="LabelSkuTextBlock"
                                   Text="e06-女式牛仔裤-牛仔蓝-S"
                                   FontFamily="Consolas"
                                   FontSize="18"
                                   FontWeight="Medium"
                                   Foreground="#1e293b"
                                   TextAlignment="Center"
                                   TextWrapping="Wrap"
                                   Margin="0,0,0,30" />

                        <!-- 数量信息 -->
                        <TextBlock x:Name="LabelQuantityTextBlock"
                                   Text="100 件"
                                   FontSize="32"
                                   FontWeight="Bold"
                                   Foreground="#1e293b"
                                   TextAlignment="Center"
                                   Margin="0,0,0,30" />

                        <!-- 二维码区域 -->
                        <Border Background="White"
                                BorderBrush="#e2e8f0"
                                BorderThickness="1"
                                CornerRadius="4"
                                Width="150"
                                Height="150"
                                HorizontalAlignment="Center">
                            <Image x:Name="QrCodeImage"
                                   Width="140"
                                   Height="140"
                                   Stretch="Uniform"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center" />
                        </Border>

                        <!-- 二维码数据显示（用于调试） -->
                        <TextBlock x:Name="QrDataTextBlock"
                                   Text="e06-牛仔蓝-s-100-件"
                                   FontSize="10"
                                   Foreground="#94a3b8"
                                   TextAlignment="Center"
                                   TextWrapping="Wrap"
                                   Margin="0,10,0,0" />
                    </StackPanel>
                </Border>
            </StackPanel>
        </Border>
    </Grid>
</Window>