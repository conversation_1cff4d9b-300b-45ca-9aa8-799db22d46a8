using System;
using System.Collections.Generic;

namespace wpfprinterapp
{
    /// <summary>
    /// 简单的测试类来验证产品逻辑
    /// </summary>
    public class TestApp
    {
        public static void TestProductLogic()
        {
            // 产品数据映射
            var productMap = new Dictionary<string, ProductInfo>
            {
                { "e06 - 女式牛仔裤", new ProductInfo("e06", "女式牛仔裤", "件") },
                { "e10 - 男士T恤", new ProductInfo("e10", "男士T恤", "件") },
                { "e12 - 运动鞋", new ProductInfo("e12", "运动鞋", "双") },
                { "e15 - 西装套装", new ProductInfo("e15", "西装套装", "套") }
            };

            // 尺码数据
            var sizeOptions = new Dictionary<string, string[]>
            {
                { "char", new[] { "S", "M", "L", "XL", "XXL", "XXXL" } },
                { "num", new[] { "110", "120", "130", "140", "150", "160", "170", "180" } }
            };

            // 测试产品信息
            Console.WriteLine("=== 产品信息测试 ===");
            foreach (var product in productMap)
            {
                Console.WriteLine($"产品: {product.Key}");
                Console.WriteLine($"  编号: {product.Value.Code}");
                Console.WriteLine($"  名称: {product.Value.Name}");
                Console.WriteLine($"  单位: {product.Value.Unit}");
                Console.WriteLine();
            }

            // 测试尺码选项
            Console.WriteLine("=== 尺码选项测试 ===");
            foreach (var sizeType in sizeOptions)
            {
                Console.WriteLine($"{sizeType.Key}尺码: {string.Join(", ", sizeType.Value)}");
            }
            Console.WriteLine();

            // 测试标签生成
            Console.WriteLine("=== 标签生成测试 ===");
            var testProduct = productMap["e06 - 女式牛仔裤"];
            var testColor = "牛仔蓝";
            var testSize = "M";
            var testQuantity = "100";

            var sku = $"{testProduct.Code}-{testProduct.Name}-{testColor}-{testSize}";
            var quantityDisplay = $"{testQuantity} {testProduct.Unit}";
            var qrData = $"{testProduct.Code}-{testColor.ToLower().Replace(" ", "")}-{testSize}-{testQuantity}-{testProduct.Unit.ToLower()}";

            Console.WriteLine($"SKU: {sku}");
            Console.WriteLine($"数量显示: {quantityDisplay}");
            Console.WriteLine($"二维码数据: {qrData}");
        }
    }
}
