using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using QRCoder;

namespace wpfprinterapp
{
    /// <summary>
    /// 智能标签打印系统主窗口
    /// </summary>
    public partial class MainWindow : Window
    {
        // 产品数据映射
        private readonly Dictionary<string, ProductInfo> _productMap = new()
        {
            { "e06 - 女式牛仔裤", new ProductInfo("e06", "女式牛仔裤", "件") },
            { "e10 - 男士T恤", new ProductInfo("e10", "男士T恤", "件") },
            { "b12 - 运动鞋", new ProductInfo("e12", "运动鞋", "双") },
            { "e15 - 西装套装", new ProductInfo("e15", "西装套装", "套") }
        };

        // 尺码数据
        private readonly Dictionary<string, string[]> _sizeOptions = new()
        {
            { "char", new[] { "S", "M", "L", "XL", "XXL", "XXXL" } },
            { "num", new[] { "110", "120", "130", "140", "150", "160", "170", "180" } }
        };

        // 颜色选项
        private readonly string[] _colorOptions = { "黑色", "白色", "牛仔蓝", "卡其色", "红色", "绿色", "灰色" };

        private string _lastValidProduct = "e06 - 女式牛仔裤";

        // 用于过滤的原始产品列表
        private List<string> _allProductCodes;

        public MainWindow()
        {
            InitializeComponent();
            InitProductCodeList(); // 必须先初始化原始列表
            InitializeData();
            BindEvents();
            UpdatePreview();
        }
        
        private void InitProductCodeList()
        {
            // 初始化原始产品编号列表
            _allProductCodes = _productMap.Keys.ToList();
        }

        /// <summary>
        /// 绑定事件处理器
        /// </summary>
        private void BindEvents()
        {
            // 绑定事件处理器
            ProductCodeComboBox.SelectionChanged += ProductCodeComboBox_SelectionChanged;
            ProductCodeComboBox.KeyDown += ProductCodeComboBox_KeyDown;
            ProductCodeComboBox.DropDownClosed += ProductCodeComboBox_DropDownClosed;
            ProductCodeComboBox.DropDownOpened += ProductCodeComboBox_DropDownOpened;
            ProductCodeComboBox.GotFocus += ProductCodeComboBox_GotFocus;
            ColorComboBox.SelectionChanged += ColorComboBox_SelectionChanged;
            SizeComboBox.SelectionChanged += SizeComboBox_SelectionChanged;
            QuantityTextBox.TextChanged += QuantityTextBox_TextChanged;
            CharSizeRadio.Checked += SizeTypeRadio_Checked;
            NumSizeRadio.Checked += SizeTypeRadio_Checked;

            // 绑定输入过滤
            ProductCodeComboBox.Loaded += ProductCodeComboBox_Loaded;
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            // 初始化产品编号下拉框与 ItemsSource
            ProductCodeComboBox.ItemsSource = _allProductCodes;

            // 初始化颜色下拉框
            foreach (var color in _colorOptions)
            {
                ColorComboBox.Items.Add(color);
            }
            //ColorComboBox.SelectedItem = "牛仔蓝";

            // 初始化尺码
            UpdateSizeOptions();
        }

        /// <summary>
        /// 更新尺码选项
        /// </summary>
        private void UpdateSizeOptions()
        {
            // 检查控件是否已初始化
            if (SizeComboBox == null || CharSizeRadio == null)
                return;

            var sizeType = CharSizeRadio.IsChecked == true ? "char" : "num";
            var currentSize = SizeComboBox.SelectedItem?.ToString();

            SizeComboBox.Items.Clear();
            foreach (var size in _sizeOptions[sizeType])
            {
                SizeComboBox.Items.Add(size);
            }

            // 保持当前选择或选择第一个
            if (!string.IsNullOrEmpty(currentSize) && _sizeOptions[sizeType].Contains(currentSize))
            {
                SizeComboBox.SelectedItem = currentSize;
            }
            else
            {
                SizeComboBox.SelectedIndex = 0;
            }

            UpdatePreview();
        }

        /// <summary>
        /// 更新标签预览
        /// </summary>
        private void UpdatePreview()
        {
            try
            {
                // 检查所有必要的控件是否已初始化
                if (ProductCodeComboBox == null || ColorComboBox == null ||
                    SizeComboBox == null || QuantityTextBox == null ||
                    UnitTextBox == null || LabelSkuTextBlock == null ||
                    LabelQuantityTextBlock == null || QrDataTextBlock == null ||
                    QrCodeImage == null)
                {
                    return;
                }

                var productDisplayValue = ProductCodeComboBox.Text ?? "";
                if (string.IsNullOrEmpty(productDisplayValue) || !_productMap.ContainsKey(productDisplayValue))
                {
                    return; // 无效产品时不更新预览
                }

                var productInfo = _productMap[productDisplayValue];
                var color = ColorComboBox.Text ?? "";
                var size = SizeComboBox.SelectedItem?.ToString() ?? "";
                var quantity = QuantityTextBox.Text ?? "0";

                // 更新单位
                UnitTextBox.Text = productInfo.Unit;

                // 更新SKU显示
                LabelSkuTextBlock.Text = $"{productInfo.Code}-{productInfo.Name}-{color}-{size}";

                // 更新数量显示
                LabelQuantityTextBlock.Text = $"{quantity} {productInfo.Unit}";

                // 更新二维码数据和图片
                var qrData = $"{productInfo.Code}-{color.ToLower().Replace(" ", "")}-{size}-{quantity}-{productInfo.Unit.ToLower()}";
                QrDataTextBlock.Text = qrData;

                // 生成二维码图片
                GenerateQrCode(qrData);
            }
            catch (Exception ex)
            {
                // 处理异常，可以记录日志或显示错误信息
                System.Diagnostics.Debug.WriteLine($"更新预览时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成二维码图片
        /// </summary>
        /// <param name="data">二维码数据</param>
        private void GenerateQrCode(string data)
        {
            try
            {
                using (var qrGenerator = new QRCodeGenerator())
                {
                    var qrCodeData = qrGenerator.CreateQrCode(data, QRCodeGenerator.ECCLevel.Q);
                    using (var qrCode = new QRCode(qrCodeData))
                    {
                        using (var qrCodeBitmap = qrCode.GetGraphic(20))
                        {
                            // 将Bitmap转换为BitmapImage
                            var bitmapImage = ConvertBitmapToBitmapImage(qrCodeBitmap);
                            QrCodeImage.Source = bitmapImage;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"生成二维码时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 将System.Drawing.Bitmap转换为WPF的BitmapImage
        /// </summary>
        /// <param name="bitmap">要转换的Bitmap</param>
        /// <returns>转换后的BitmapImage</returns>
        private BitmapImage ConvertBitmapToBitmapImage(Bitmap bitmap)
        {
            using (var memory = new MemoryStream())
            {
                bitmap.Save(memory, System.Drawing.Imaging.ImageFormat.Png);
                memory.Position = 0;

                var bitmapImage = new BitmapImage();
                bitmapImage.BeginInit();
                bitmapImage.StreamSource = memory;
                bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                bitmapImage.EndInit();
                bitmapImage.Freeze();

                return bitmapImage;
            }
        }

        // ComboBox 加载后绑定 TextBox.TextChanged
        private void ProductCodeComboBox_Loaded(object sender, RoutedEventArgs e)
        {
            var textBox = ProductCodeComboBox.Template.FindName("PART_EditableTextBox", ProductCodeComboBox) as TextBox;
            if (textBox != null)
            {
                textBox.TextChanged -= ProductCodeComboBox_TextChanged;
                textBox.TextChanged += ProductCodeComboBox_TextChanged;
            }
        }

        // 输入过滤和自动弹出
        private void ProductCodeComboBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var tb = sender as TextBox;
            if (tb == null) return;

            // 获取用户实际输入的文本（不包括自动完成的部分）
            string input = tb.Text;

            // 如果有选中的文本，说明是自动完成，只取未选中的部分
            if (tb.SelectionLength > 0 && tb.SelectionStart > 0)
            {
                input = tb.Text.Substring(0, tb.SelectionStart);
            }

            System.Diagnostics.Debug.WriteLine($"用户输入: '{input}', 完整文本: '{tb.Text}', 选择开始: {tb.SelectionStart}, 选择长度: {tb.SelectionLength}");

            // 使用集合视图进行过滤，避免清空 Items 导致文本丢失
            ICollectionView view = CollectionViewSource.GetDefaultView(ProductCodeComboBox.ItemsSource);
            if (string.IsNullOrWhiteSpace(input))
            {
                view.Filter = null; // 显示全部
            }
            else
            {
                view.Filter = item => ((string)item).StartsWith(input, StringComparison.OrdinalIgnoreCase);
            }

            // 调试输出过滤结果
            var filteredList = _allProductCodes.Where(x => string.IsNullOrWhiteSpace(input) || x.StartsWith(input, StringComparison.OrdinalIgnoreCase)).ToList();
            System.Diagnostics.Debug.WriteLine($"Filter '{input}' => {string.Join(", ", filteredList)}");

            // 输入时自动弹出
            ProductCodeComboBox.IsDropDownOpen = true;
        }

        // 事件处理器
        private void ProductCodeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ProductCodeComboBox.SelectedItem != null)
            {
                _lastValidProduct = ProductCodeComboBox.SelectedItem.ToString()!;
                UpdatePreview();
            }
        }

        private void ProductCodeComboBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                // 处理回车键选择
                var comboBox = sender as ComboBox;
                if (comboBox != null)
                {
                    var text = comboBox.Text;

                    // 查找匹配的项目
                    foreach (var item in comboBox.Items)
                    {
                        var itemText = item.ToString();
                        if (itemText != null && itemText.StartsWith(text, StringComparison.OrdinalIgnoreCase))
                        {
                            comboBox.SelectedItem = item;
                            comboBox.IsDropDownOpen = false;
                            UpdatePreview();
                            e.Handled = true;
                            return;
                        }
                    }

                    // 如果没有找到匹配项，但文本完全匹配某个项目
                    if (_productMap.ContainsKey(text))
                    {
                        comboBox.SelectedItem = text;
                        _lastValidProduct = text;
                        UpdatePreview();
                    }

                    e.Handled = true;
                }
            }
        }

        private void ProductCodeComboBox_DropDownClosed(object? sender, EventArgs e)
        {
            //下拉框关闭时验证选择
            var comboBox = sender as ComboBox;
            if (comboBox != null)
            {
                var text = comboBox.Text;
                if (_productMap.ContainsKey(text))
                {
                    _lastValidProduct = text;
                    comboBox.SelectedItem = text;
                    UpdatePreview();
                }
                else if (!string.IsNullOrEmpty(_lastValidProduct))
                {
                    // 如果输入的不是有效产品，恢复到上一个有效选择
                    comboBox.Text = _lastValidProduct;
                    comboBox.SelectedItem = _lastValidProduct;
                }
            }
        }

        private void ProductCodeComboBox_DropDownOpened(object? sender, EventArgs e)
        {
            // 下拉框打开时清除文本选择
            var comboBox = sender as ComboBox;
            if (comboBox != null && comboBox.IsEditable)
            {
                // 延迟执行以确保在UI更新后清除选择
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    try
                    {
                        var textBox = comboBox.Template?.FindName("PART_EditableTextBox", comboBox) as TextBox;
                        if (textBox != null && !string.IsNullOrEmpty(textBox.Text))
                        {
                            textBox.SelectionLength = 0;
                            textBox.SelectionStart = textBox.Text.Length; // 将光标移到文本末尾
                        }
                    }
                    catch (Exception ex)
                    {
                        // 忽略异常，避免影响用户体验
                        System.Diagnostics.Debug.WriteLine($"清除文本选择时发生错误: {ex.Message}");
                    }
                }), System.Windows.Threading.DispatcherPriority.Background);
            }
        }

        private void ProductCodeComboBox_GotFocus(object sender, RoutedEventArgs e)
        {
            // 获得焦点时防止自动选择文本
            var comboBox = sender as ComboBox;
            if (comboBox != null && comboBox.IsEditable)
            {
                // 延迟执行以确保在UI更新后清除选择
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    try
                    {
                        var textBox = comboBox.Template?.FindName("PART_EditableTextBox", comboBox) as TextBox;
                        if (textBox != null && !string.IsNullOrEmpty(textBox.Text))
                        {
                            textBox.SelectionLength = 0;
                            textBox.SelectionStart = textBox.Text.Length; // 将光标移到文本末尾
                        }
                    }
                    catch (Exception ex)
                    {
                        // 忽略异常，避免影响用户体验
                        System.Diagnostics.Debug.WriteLine($"清除文本选择时发生错误: {ex.Message}");
                    }
                }), System.Windows.Threading.DispatcherPriority.Background);
            }
        }

        private void ColorComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void SizeTypeRadio_Checked(object sender, RoutedEventArgs e)
        {
            UpdateSizeOptions();
        }

        private void SizeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void QuantityTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var printCount = int.Parse(PrintCountTextBox.Text);
                var productName = ProductCodeComboBox.Text;
                var color = ColorComboBox.Text;
                var size = SizeComboBox.SelectedItem?.ToString();
                var quantity = QuantityTextBox.Text;
                var unit = UnitTextBox.Text;

                var message = $"准备打印 {printCount} 张标签:\n" +
                             $"产品: {productName}\n" +
                             $"颜色: {color}\n" +
                             $"尺码: {size}\n" +
                             $"数量: {quantity} {unit}";

                MessageBox.Show(message, "打印确认", MessageBoxButton.OK, MessageBoxImage.Information);

                // TODO: 在这里实现实际的打印逻辑
                // 可以调用打印机API或生成打印文件
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打印时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    /// <summary>
    /// 产品信息类
    /// </summary>
    public class ProductInfo
    {
        public string Code { get; }
        public string Name { get; }
        public string Unit { get; }

        public ProductInfo(string code, string name, string unit)
        {
            Code = code;
            Name = name;
            Unit = unit;
        }
    }
}