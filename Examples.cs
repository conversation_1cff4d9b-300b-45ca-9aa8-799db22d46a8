using System;
using System.Collections.Generic;

namespace wpfprinterapp.Examples
{
    /// <summary>
    /// 使用示例和扩展示例
    /// </summary>
    public static class Examples
    {
        /// <summary>
        /// 示例：如何添加新的产品类型
        /// </summary>
        public static Dictionary<string, ProductInfo> GetExtendedProductMap()
        {
            return new Dictionary<string, ProductInfo>
            {
                // 原有产品
                { "e06 - 女式牛仔裤", new ProductInfo("e06", "女式牛仔裤", "件") },
                { "e10 - 男士T恤", new ProductInfo("e10", "男士T恤", "件") },
                { "e12 - 运动鞋", new ProductInfo("e12", "运动鞋", "双") },
                { "e15 - 西装套装", new ProductInfo("e15", "西装套装", "套") },
                
                // 新增产品示例
                { "e20 - 女式连衣裙", new ProductInfo("e20", "女式连衣裙", "件") },
                { "e21 - 男士衬衫", new ProductInfo("e21", "男士衬衫", "件") },
                { "e22 - 儿童外套", new ProductInfo("e22", "儿童外套", "件") },
                { "e23 - 运动袜", new ProductInfo("e23", "运动袜", "双") },
                { "e24 - 手套", new ProductInfo("e24", "手套", "双") },
                { "e25 - 围巾", new ProductInfo("e25", "围巾", "条") },
                { "e26 - 帽子", new ProductInfo("e26", "帽子", "顶") },
                { "e27 - 内衣套装", new ProductInfo("e27", "内衣套装", "套") }
            };
        }

        /// <summary>
        /// 示例：扩展的颜色选项
        /// </summary>
        public static string[] GetExtendedColorOptions()
        {
            return new[]
            {
                // 基础颜色
                "黑色", "白色", "灰色", "红色", "绿色", "蓝色",
                
                // 扩展颜色
                "牛仔蓝", "卡其色", "米色", "咖啡色", "粉色", "紫色",
                "橙色", "黄色", "深蓝", "浅蓝", "深灰", "浅灰",
                "酒红", "墨绿", "藏青", "奶白", "象牙白", "珍珠白"
            };
        }

        /// <summary>
        /// 示例：扩展的尺码选项
        /// </summary>
        public static Dictionary<string, string[]> GetExtendedSizeOptions()
        {
            return new Dictionary<string, string[]>
            {
                // 字符尺码 - 成人服装
                { "char_adult", new[] { "XS", "S", "M", "L", "XL", "XXL", "XXXL", "XXXXL" } },
                
                // 字符尺码 - 儿童服装
                { "char_child", new[] { "90", "100", "110", "120", "130", "140", "150", "160" } },
                
                // 数字尺码 - 成人服装
                { "num_adult", new[] { "155", "160", "165", "170", "175", "180", "185", "190" } },
                
                // 数字尺码 - 儿童服装
                { "num_child", new[] { "90", "95", "100", "105", "110", "115", "120", "125", "130", "135", "140", "145", "150", "155", "160" } },
                
                // 鞋码 - 中国码
                { "shoe_cn", new[] { "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46" } },
                
                // 鞋码 - 欧码
                { "shoe_eu", new[] { "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47" } },
                
                // 鞋码 - 美码
                { "shoe_us", new[] { "5", "5.5", "6", "6.5", "7", "7.5", "8", "8.5", "9", "9.5", "10", "10.5", "11", "11.5", "12" } }
            };
        }

        /// <summary>
        /// 示例：生成标准SKU代码
        /// </summary>
        /// <param name="productCode">产品编号</param>
        /// <param name="color">颜色</param>
        /// <param name="size">尺码</param>
        /// <param name="quantity">数量</param>
        /// <param name="unit">单位</param>
        /// <returns>标准SKU代码</returns>
        public static string GenerateStandardSku(string productCode, string color, string size, int quantity, string unit)
        {
            // 颜色代码映射
            var colorCodes = new Dictionary<string, string>
            {
                { "黑色", "BK" }, { "白色", "WH" }, { "红色", "RD" }, { "蓝色", "BL" },
                { "绿色", "GR" }, { "黄色", "YL" }, { "紫色", "PP" }, { "粉色", "PK" },
                { "灰色", "GY" }, { "咖啡色", "BR" }, { "牛仔蓝", "DB" }, { "卡其色", "KH" }
            };

            // 单位代码映射
            var unitCodes = new Dictionary<string, string>
            {
                { "件", "PC" }, { "双", "PR" }, { "套", "ST" }, { "条", "SP" }, { "顶", "TP" }
            };

            var colorCode = colorCodes.ContainsKey(color) ? colorCodes[color] : color.Substring(0, Math.Min(2, color.Length)).ToUpper();
            var unitCode = unitCodes.ContainsKey(unit) ? unitCodes[unit] : unit.Substring(0, Math.Min(2, unit.Length)).ToUpper();

            return $"{productCode}-{colorCode}-{size}-{quantity:D3}-{unitCode}";
        }

        /// <summary>
        /// 示例：解析SKU代码
        /// </summary>
        /// <param name="skuCode">SKU代码</param>
        /// <returns>解析后的SKU信息</returns>
        public static SkuInfo ParseSkuCode(string skuCode)
        {
            var parts = skuCode.Split('-');
            if (parts.Length != 5)
            {
                throw new ArgumentException("无效的SKU代码格式");
            }

            return new SkuInfo
            {
                ProductCode = parts[0],
                ColorCode = parts[1],
                Size = parts[2],
                Quantity = int.Parse(parts[3]),
                UnitCode = parts[4]
            };
        }

        /// <summary>
        /// 示例：批量生成SKU
        /// </summary>
        /// <param name="productCode">产品编号</param>
        /// <param name="colors">颜色列表</param>
        /// <param name="sizes">尺码列表</param>
        /// <param name="defaultQuantity">默认数量</param>
        /// <param name="unit">单位</param>
        /// <returns>SKU列表</returns>
        public static List<string> GenerateBatchSkus(string productCode, string[] colors, string[] sizes, int defaultQuantity, string unit)
        {
            var skus = new List<string>();
            
            foreach (var color in colors)
            {
                foreach (var size in sizes)
                {
                    var sku = GenerateStandardSku(productCode, color, size, defaultQuantity, unit);
                    skus.Add(sku);
                }
            }
            
            return skus;
        }
    }

    /// <summary>
    /// SKU信息类
    /// </summary>
    public class SkuInfo
    {
        public string ProductCode { get; set; } = "";
        public string ColorCode { get; set; } = "";
        public string Size { get; set; } = "";
        public int Quantity { get; set; }
        public string UnitCode { get; set; } = "";

        public override string ToString()
        {
            return $"{ProductCode}-{ColorCode}-{Size}-{Quantity:D3}-{UnitCode}";
        }
    }

    /// <summary>
    /// 打印配置类
    /// </summary>
    public class PrintConfig
    {
        public string PrinterName { get; set; } = "";
        public int Copies { get; set; } = 1;
        public bool PrintQrCode { get; set; } = true;
        public bool PrintSkuText { get; set; } = true;
        public bool PrintQuantity { get; set; } = true;
        public string LabelSize { get; set; } = "40x30"; // mm
    }
}
