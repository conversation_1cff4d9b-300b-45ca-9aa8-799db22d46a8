# 智能标签打印系统 - 使用指南

## 系统概述

这是一个专为仓库SKU管理设计的现代化标签打印系统，具有以下特点：

- 🎯 **专业设计**: 基于实际仓库管理需求设计
- 🔄 **实时预览**: 所见即所得的标签预览
- 📱 **二维码支持**: 自动生成包含完整SKU信息的二维码
- 🎨 **现代界面**: 采用现代化的WPF界面设计
- ⚡ **高效操作**: 简化的操作流程，提高工作效率

## 界面布局

### 左侧配置面板
```
┌─────────────────────────┐
│        配置项           │
├─────────────────────────┤
│ 产品编号: [下拉选择]     │
│ 颜色:     [下拉选择]     │
│ 尺码类型: ○字符 ○数字   │
│ 尺码:     [下拉选择]     │
│ 单位:     [自动填充]     │
│ 库存数量: [输入框]       │
│ 打印张数: [输入框]       │
│                         │
│      [打 印]            │
└─────────────────────────┘
```

### 右侧预览面板
```
┌─────────────────────────┐
│       标签预览          │
├─────────────────────────┤
│                         │
│   e06-女式牛仔裤-蓝-M   │
│                         │
│       100 件            │
│                         │
│     ┌─────────┐         │
│     │ QR CODE │         │
│     │  IMAGE  │         │
│     └─────────┘         │
│                         │
└─────────────────────────┘
```

## 操作步骤

### 1. 选择产品
- 点击"产品编号"下拉框
- 选择需要的产品（如：e06 - 女式牛仔裤）
- 系统会自动填充对应的单位

### 2. 设置颜色
- 在"颜色"下拉框中选择颜色
- 也可以直接输入自定义颜色

### 3. 选择尺码
- 首先选择尺码类型：
  - **字符尺码**: S, M, L, XL, XXL, XXXL
  - **数字尺码**: 110, 120, 130, 140, 150, 160, 170, 180
- 然后在"尺码"下拉框中选择具体尺码

### 4. 输入数量信息
- **库存数量**: 输入当前库存数量
- **打印张数**: 输入需要打印的标签数量

### 5. 预览和打印
- 右侧面板会实时显示标签预览
- 确认信息无误后，点击"打印"按钮

## SKU编码规则

### SKU显示格式
```
编号-产品名-颜色-尺码
```
示例：`e06-女式牛仔裤-牛仔蓝-M`

### 二维码数据格式
```
编号-颜色-尺码-数量-单位
```
示例：`e06-牛仔蓝-m-100-件`

## 产品类型说明

| 编号 | 产品名称 | 单位 | 说明 |
|------|----------|------|------|
| e06  | 女式牛仔裤 | 件 | 女装类 |
| e10  | 男士T恤 | 件 | 男装类 |
| e12  | 运动鞋 | 双 | 鞋类 |
| e15  | 西装套装 | 套 | 套装类 |

## 尺码对照表

### 字符尺码（服装类）
| 尺码 | 适用身高 | 说明 |
|------|----------|------|
| S    | 155-160cm | 小号 |
| M    | 160-165cm | 中号 |
| L    | 165-170cm | 大号 |
| XL   | 170-175cm | 加大号 |
| XXL  | 175-180cm | 加加大号 |
| XXXL | 180cm以上 | 特大号 |

### 数字尺码（儿童类）
| 尺码 | 适用年龄 | 适用身高 |
|------|----------|----------|
| 110  | 3-4岁 | 105-115cm |
| 120  | 4-5岁 | 115-125cm |
| 130  | 5-6岁 | 125-135cm |
| 140  | 6-7岁 | 135-145cm |
| 150  | 7-8岁 | 145-155cm |
| 160  | 8-9岁 | 155-165cm |
| 170  | 9-10岁 | 165-175cm |
| 180  | 10岁以上 | 175cm以上 |

## 常见问题

### Q: 如何添加新的产品？
A: 需要在代码中的`_productMap`字典中添加新的产品信息。

### Q: 可以自定义颜色吗？
A: 可以，颜色下拉框支持输入自定义颜色。

### Q: 二维码包含什么信息？
A: 二维码包含完整的SKU信息：编号-颜色-尺码-数量-单位。

### Q: 如何连接实际打印机？
A: 当前版本显示打印确认对话框，实际打印功能需要进一步开发。

### Q: 支持批量打印吗？
A: 支持，可以在"打印张数"中设置需要打印的数量。

## 技术支持

如需技术支持或功能扩展，请联系开发团队。

### 系统要求
- Windows 10/11
- .NET 8.0 Runtime
- 支持WPF的显示器

### 性能建议
- 建议内存：4GB以上
- 建议分辨率：1920x1080以上
- 建议处理器：双核以上
