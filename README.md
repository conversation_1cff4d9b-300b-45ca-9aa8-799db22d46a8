# 智能标签打印系统

一个现代化的WPF标签打印程序，专为仓库SKU管理设计。

## 功能特性

### 核心功能
- **产品管理**: 支持多种产品类型（女式牛仔裤、男士T恤、运动鞋、西装套装等）
- **SKU组合**: 编号 + 颜色 + 尺码 + 单位的完整SKU体系
- **二维码生成**: 自动生成包含完整SKU信息的二维码
- **实时预览**: 所见即所得的标签预览功能
- **批量打印**: 支持指定打印张数

### SKU组成要素
1. **产品编号**: e06、e10、e12、e15等
2. **颜色**: 黑色、白色、牛仔蓝、卡其色、红色、绿色、灰色
3. **尺码**: 
   - 字符尺码: S、M、L、XL、XXL、XXXL
   - 数字尺码: 110、120、130、140、150、160、170、180
4. **单位**: 条、件、套、双

## 界面说明

### 左侧配置面板
- **产品编号**: 下拉选择或输入产品编号
- **颜色**: 选择或输入颜色
- **尺码类型**: 选择字符尺码或数字尺码
- **尺码**: 根据尺码类型显示相应选项
- **单位**: 根据产品自动填充（只读）
- **库存数量**: 输入当前库存数量
- **打印张数**: 指定要打印的标签数量

### 右侧预览面板
- **SKU显示**: 格式为"编号-产品名-颜色-尺码"
- **数量显示**: 格式为"数量 单位"
- **二维码**: 包含完整SKU信息的二维码图片

## 技术实现

### 开发环境
- .NET 8.0
- WPF (Windows Presentation Foundation)
- C# 12

### 主要依赖
- **QRCoder**: 用于生成二维码图片
- **System.Drawing.Common**: 图像处理支持

### 项目结构
```
wpfprinterapp/
├── MainWindow.xaml          # 主界面XAML
├── MainWindow.xaml.cs       # 主界面逻辑
├── App.xaml                 # 应用程序配置
├── App.xaml.cs              # 应用程序入口
├── TestApp.cs               # 测试类
└── design/                  # 设计文件
    └── web_printer_1_1.html # 原始HTML设计
```

## 使用方法

1. **启动应用程序**
   ```bash
   dotnet run
   ```

2. **配置标签信息**
   - 选择产品编号
   - 选择或输入颜色
   - 选择尺码类型和具体尺码
   - 输入库存数量
   - 设置打印张数

3. **预览标签**
   - 右侧面板实时显示标签预览
   - 包含SKU信息、数量和二维码

4. **打印标签**
   - 点击"打印"按钮
   - 系统将显示打印确认信息

## 二维码数据格式

二维码包含的数据格式：
```
编号-颜色-尺码-数量-单位
```

示例：
```
e06-牛仔蓝-m-100-件
```

## 扩展功能

### 未来可扩展的功能
- [ ] 连接实际打印机
- [ ] 从服务端获取产品数据
- [ ] 打印历史记录
- [ ] 标签模板自定义
- [ ] 批量导入SKU数据
- [ ] 库存管理集成

## 开发说明

### 编译项目
```bash
dotnet build
```

### 运行项目
```bash
dotnet run
```

### 添加新产品
在 `MainWindow.xaml.cs` 的 `_productMap` 字典中添加新的产品信息：
```csharp
{ "产品代码 - 产品名称", new ProductInfo("代码", "名称", "单位") }
```

### 修改颜色选项
在 `_colorOptions` 数组中添加或修改颜色选项。

### 修改尺码选项
在 `_sizeOptions` 字典中修改字符尺码或数字尺码的选项。
