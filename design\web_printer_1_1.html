
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能标签打印系统 - Web版 (V1.1)</title>
    <link rel="stylesheet" href="theme_web_1.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&family=JetBrains+Mono:wght@400&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: var(--background);
            color: var(--foreground);
            font-family: var(--font-sans);
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1.2fr;
            gap: 20px;
            width: 100%;
            max-width: 1200px;
            background-color: var(--card);
            border-radius: var(--radius);
            overflow: hidden;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border);
        }
        .panel {
            padding: 30px;
        }
        .config-panel {
            border-right: 1px solid var(--border);
        }
        h1, h2 {
            margin-top: 0;
            font-weight: 600;
        }
        h1 { font-size: 1.5rem; }
        h2 { font-size: 1.25rem; color: var(--muted-foreground); }

        .form-group {
            margin-bottom: 1.25rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            color: var(--muted-foreground);
        }
        .form-control {
            width: 100%;
            padding: 0.75rem;
            background-color: var(--input);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            color: var(--foreground);
            font-family: inherit;
            font-size: 1rem;
            box-sizing: border-box;
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px var(--ring);
        }
        .form-control[readonly] {
            background-color: var(--muted);
            cursor: not-allowed;
        }
        .radio-group {
            display: flex;
            gap: 1rem;
        }
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        .btn {
            width: 100%;
            padding: 0.875rem;
            background-color: var(--primary);
            color: var(--primary-foreground);
            border: none;
            border-radius: var(--radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: opacity 0.2s;
        }
        .btn:hover {
            opacity: 0.9;
        }

        .preview-panel .label-preview {
            background-color: white;
            border-radius: var(--radius);
            padding: 2rem;
            color: black;
            text-align: center;
            min-height: 300px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 1.5rem;
        }
        .label-sku {
            font-family: var(--font-mono);
            font-size: 1.1rem;
            word-break: break-all;
        }
        .label-quantity {
            font-family: var(--font-sans);
            font-size: 2rem;
            font-weight: 700;
        }
        .label-qr img {
            display: block;
            width: 150px;
            height: 150px;
            border: 1px solid #eee;
        }
    </style>
</head>
<body>

    <div class="container">
        <div class="panel config-panel">
            <h1>配置项</h1>
            <div class="form-group">
                <label for="product-code">产品编号</label>
                <input type="text" id="product-code" class="form-control" list="product-code-list" autocomplete="off">
                <datalist id="product-code-list">
                    <option>e06 - 女式牛仔裤</option>
                    <option>e10 - 男士T恤</option>
                </datalist>
            </div>
            <div class="form-group">
                <label for="color">颜色</label>
                <input type="text" id="color" class="form-control" value="牛仔蓝" list="color-list">
                <datalist id="color-list">
                    <option value="黑色"></option>
                    <option value="白色"></option>
                    <option value="牛仔蓝"></option>
                    <option value="卡其色"></option>
                </datalist>
            </div>
            <div class="form-group">
                <label>尺码类型</label>
                <div class="radio-group">
                    <label><input type="radio" name="size-type" value="char" checked> 字符尺码</label>
                    <label><input type="radio" name="size-type" value="num"> 数字尺码</label>
                </div>
            </div>
            <div class="form-group">
                <label for="size">尺码</label>
                <select id="size" class="form-control"></select>
            </div>
            <div class="grid-2">
                <div class="form-group">
                    <label for="unit">单位</label>
                    <input type="text" id="unit" class="form-control" value="件" readonly>
                </div>
                <div class="form-group">
                    <label for="quantity">库存数量</label>
                    <input type="number" id="quantity" class="form-control" value="100">
                </div>
            </div>
            <div class="form-group">
                <label for="print-count">打印张数</label>
                <input type="number" id="print-count" class="form-control" value="5">
            </div>
            <button class="btn" onclick="window.print()">打 印</button>
        </div>
        <div class="panel preview-panel">
            <h2>标签预览</h2>
            <div class="label-preview">
                <div id="label-sku" class="label-sku"></div>
                <div id="label-quantity" class="label-quantity"></div>
                <div id="label-qr" class="label-qr">
                    <img src="" alt="QR Code">
                </div>
            </div>
        </div>
    </div>

    <script>
        const sizes = {
            char: ['S', 'M', 'L', 'XL', 'XXL', 'XXXL'],
            num: ['110', '120', '130', '140', '150', '160', '170', '180']
        };

        const productMap = new Map([
            ['e06 - 女式牛仔裤', 'e06-女式牛仔裤-件'],
            ['e10 - 男士T恤', 'e10-男士T恤-件']
        ]);
        let lastValidProduct = 'e06 - 女式牛仔裤';

        const productCodeEl = document.getElementById('product-code');
        const colorEl = document.getElementById('color');
        const sizeTypeEls = document.querySelectorAll('input[name="size-type"]');
        const sizeEl = document.getElementById('size');
        const unitEl = document.getElementById('unit');
        const quantityEl = document.getElementById('quantity');

        const labelSkuEl = document.getElementById('label-sku');
        const labelQuantityEl = document.getElementById('label-quantity');
        const labelQrImgEl = document.querySelector('#label-qr img');

        function updateSizeOptions() {
            const selectedType = document.querySelector('input[name="size-type"]:checked').value;
            const currentSizeValue = sizeEl.value;
            sizeEl.innerHTML = '';
            sizes[selectedType].forEach(s => {
                const option = document.createElement('option');
                option.value = s;
                option.textContent = s;
                sizeEl.appendChild(option);
            });
            
            if (sizes[selectedType].includes(currentSizeValue)) {
                sizeEl.value = currentSizeValue;
            } else {
                sizeEl.value = sizes[selectedType][0];
            }
            updatePreview();
        }

        function updatePreview() {
            const productDisplayValue = productCodeEl.value;
            if (!productMap.has(productDisplayValue)) {
                // Don't update preview if input is not a valid product
                return;
            }
            const internalValue = productMap.get(productDisplayValue);
            const productData = internalValue.split('-');
            const code = productData[0];
            const name = productData[1];
            const unit = productData[2];

            const color = colorEl.value;
            const size = sizeEl.value;
            const quantity = quantityEl.value;

            unitEl.value = unit;

            labelSkuEl.textContent = `${code}-${name}-${color}-${size}`;
            labelQuantityEl.textContent = `${quantity} ${unit}`;

            const qrData = `${code}-${color.toLowerCase().replace(/\s/g, '')}-${size}-${quantity}-${unit.toLowerCase()}`;
            labelQrImgEl.src = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(qrData)}`;
            labelQrImgEl.alt = `QR Code for ${qrData}`;
        }

        productCodeEl.addEventListener('input', updatePreview);
        productCodeEl.addEventListener('change', () => {
            if (!productMap.has(productCodeEl.value)) {
                productCodeEl.value = lastValidProduct;
            } else {
                lastValidProduct = productCodeEl.value;
            }
            updatePreview();
        });

        colorEl.addEventListener('input', updatePreview);
        sizeTypeEls.forEach(el => el.addEventListener('change', updateSizeOptions));
        sizeEl.addEventListener('change', updatePreview);
        quantityEl.addEventListener('input', updatePreview);

        // Initial setup
        productCodeEl.value = lastValidProduct;
        updateSizeOptions();

    </script>

</body>
</html>
